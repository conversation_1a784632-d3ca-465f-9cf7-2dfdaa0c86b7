'use client';

/**
 * StructuredData component for adding JSON-LD structured data to pages
 * @param {Object} data - The structured data object
 * @returns {JSX.Element} Script tag with JSON-LD data
 */
export default function StructuredData({ data }) {
	if (!data || typeof data !== 'object' || data === null) return null;

	let jsonString;
	try {
		jsonString = JSON.stringify(data, null, 0);
	} catch (error) {
		console.warn('Invalid structured data provided:', error);
		return null;
	}

	return (
		<script
			type='application/ld+json'
			dangerouslySetInnerHTML={{
				__html: jsonString,
			}}
		/>
	);
}
