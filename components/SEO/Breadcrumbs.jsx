'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import StructuredData from './StructuredData';
import { generateBreadcrumbStructuredData } from '@/lib/seo';

/**
 * Breadcrumbs component with SEO structured data
 * @param {Array} items - Array of breadcrumb items with name and path
 * @param {string} className - Additional CSS classes
 * @returns {JSX.Element} Breadcrumbs navigation with structured data
 */
export default function Breadcrumbs({ items = [], className = '' }) {
	if (!items.length) return null;

	// Always include home as first item
	const breadcrumbItems = [
		{ name: 'Home', path: '/' },
		...items,
	];

	const structuredData = generateBreadcrumbStructuredData(breadcrumbItems);

	return (
		<>
			<StructuredData data={structuredData} />
			<nav
				aria-label="Breadcrumb"
				className={`flex items-center space-x-2 text-sm text-text/70 ${className}`}
			>
				<ol className="flex items-center space-x-2">
					{breadcrumbItems.map((item, index) => (
						<li key={item.path} className="flex items-center">
							{index > 0 && (
								<ChevronRight className="h-4 w-4 mx-2 text-text/50" />
							)}
							{index === breadcrumbItems.length - 1 ? (
								<span className="text-text font-medium" aria-current="page">
									{index === 0 && <Home className="h-4 w-4 inline mr-1" />}
									{item.name}
								</span>
							) : (
								<Link
									href={item.path}
									className="hover:text-primary transition-colors duration-200"
								>
									{index === 0 && <Home className="h-4 w-4 inline mr-1" />}
									{item.name}
								</Link>
							)}
						</li>
					))}
				</ol>
			</nav>
		</>
	);
}
