import Link from 'next/link';

const CTASection = () => {
	return (
		<section className='py-20 px-6 relative'>
			<div className='relative z-10 max-w-4xl mx-auto card-3d bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-12'>
				<div className='text-center'>
					<h2 className='text-3xl md:text-4xl font-bold mb-6 text-text'>
						Ready to Simplify Your Document Printing?
					</h2>
					<p className='text-xl text-text/80 mb-8 max-w-2xl mx-auto'>
						Join UploadDoc today and experience a streamlined approach to
						document submission and printing.
					</p>
					<div className='flex flex-wrap justify-center gap-4'>
						<Link
							href='/submit'
							className='btn-3d bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-lg font-medium hover:scale-105 transition-transform duration-300'>
							Get Started Now
						</Link>
						<Link
							href='/learn-more'
							className='glow-effect bg-primary/10 text-text px-8 py-4 rounded-lg hover:bg-primary/20 transition-all duration-300 font-medium'>
							Learn More
						</Link>
					</div>
				</div>
			</div>
		</section>
	);
};

export default CTASection;
