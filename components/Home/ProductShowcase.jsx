'use client';
import { useState } from 'react';
import {
	ChevronLeft,
	ChevronRight,
	Upload,
	Search,
	FileText,
	BarChart3,
} from 'lucide-react';

const ProductShowcase = () => {
	const [currentSlide, setCurrentSlide] = useState(0);

	const products = [
		{
			id: 1,
			title: 'Document Submission',
			description:
				'Easily upload and submit your documents with our intuitive interface. Select vendors, add notes, and track your submissions.',
			image:
				'https://ik.imagekit.io/uploaddoc/App%20Screenshot/Submitpage.png?updatedAt=1752406851007',
			icon: <Upload className='w-6 h-6' />,
			features: [
				'Drag & drop upload',
				'Vendor selection',
				'Real-time tracking',
			],
		},
		{
			id: 2,
			title: 'Submitted Documents',
			description:
				'Keep track of all your submitted documents in one place. Monitor status, view details, and manage your submissions.',
			image:
				'https://ik.imagekit.io/uploaddoc/App%20Screenshot/SubmittedDocs.png?updatedAt=1752406852523',
			icon: <FileText className='w-6 h-6' />,
			features: ['Document history', 'Status tracking', 'Quick actions'],
		},
		{
			id: 3,
			title: 'Find Vendors',
			description:
				'Discover and connect with trusted printing vendors in your area. Compare services, ratings, and pricing.',
			image:
				'https://ik.imagekit.io/uploaddoc/App%20Screenshot/findvendor.png?updatedAt=1752406858448',
			icon: <Search className='w-6 h-6' />,
			features: [
				'Location-based search',
				'Vendor ratings',
				'Service comparison',
			],
		},
		{
			id: 4,
			title: 'Vendor Dashboard',
			description:
				'Comprehensive dashboard for vendors to manage submissions, track orders, and grow their business.',
			image:
				'https://ik.imagekit.io/uploaddoc/App%20Screenshot/dashboard.png?updatedAt=1752406859151',
			icon: <BarChart3 className='w-6 h-6' />,
			features: ['Order management', 'Analytics', 'Customer communication'],
		},
	];

	const nextSlide = () => {
		setCurrentSlide((prev) => (prev + 1) % products.length);
	};

	const prevSlide = () => {
		setCurrentSlide((prev) => (prev - 1 + products.length) % products.length);
	};

	return (
		<section className='py-24 px-6 bg-gradient-to-b from-background to-primary/5'>
			<div className='max-w-7xl mx-auto'>
				<header className='text-center mb-16'>
					<div className='inline-block px-4 py-1 rounded-full bg-primary/20 text-primary text-sm font-medium mb-4 glow-effect'>
						Product Features
					</div>
					<h2 className='text-4xl font-bold mb-6 text-text'>
						Experience UploadDoc in Action
					</h2>
					<p className='text-xl text-text/80 max-w-2xl mx-auto'>
						Discover how our platform simplifies document management and
						printing for everyone
					</p>
				</header>

				<div className='relative'>
					{/* Mobile Carousel */}
					<div className='block lg:hidden'>
						<div className='relative overflow-hidden rounded-2xl'>
							<div
								className='flex transition-transform duration-500 ease-in-out'
								style={{ transform: `translateX(-${currentSlide * 100}%)` }}>
								{products.map((product) => (
									<div
										key={product.id}
										className='w-full flex-shrink-0'>
										<div className='card-3d p-8 mx-4'>
											<div className='flex items-center gap-4 mb-6'>
												<div className='btn-3d bg-gradient-to-r from-primary to-accent p-3 rounded-full text-white'>
													{product.icon}
												</div>
												<div>
													<h3 className='text-2xl font-bold text-text'>
														{product.title}
													</h3>
													<p className='text-text/70'>{product.description}</p>
												</div>
											</div>
											<div className='relative mb-6'>
												<img
													src={product.image}
													alt={product.title}
													className='w-full h-64 object-cover rounded-xl shadow-lg'
													loading='lazy'
												/>
												<div className='absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl'></div>
											</div>
											<div className='flex flex-wrap gap-2'>
												{product.features.map((feature, index) => (
													<span
														key={index}
														className='px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium'>
														{feature}
													</span>
												))}
											</div>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Navigation buttons */}
						<div className='flex justify-center items-center gap-4 mt-8'>
							<button
								onClick={prevSlide}
								className='btn-3d bg-gradient-to-r from-primary to-accent p-3 rounded-full text-white hover:opacity-90 transition-opacity'>
								<ChevronLeft className='w-5 h-5' />
							</button>
							<div className='flex gap-2'>
								{products.map((_, index) => (
									<button
										key={index}
										onClick={() => setCurrentSlide(index)}
										className={`w-2 h-2 rounded-full transition-all ${
											index === currentSlide ? 'bg-primary' : 'bg-primary/30'
										}`}
									/>
								))}
							</div>
							<button
								onClick={nextSlide}
								className='btn-3d bg-gradient-to-r from-primary to-accent p-3 rounded-full text-white hover:opacity-90 transition-opacity'>
								<ChevronRight className='w-5 h-5' />
							</button>
						</div>
					</div>

					{/* Desktop Grid */}
					<div className='hidden lg:grid lg:grid-cols-2 gap-8'>
						{products.map((product, index) => (
							<div
								key={product.id}
								className='group'>
								<div className='card-3d p-8 h-full hover:scale-105 transition-transform duration-300'>
									<div className='flex items-center gap-4 mb-6'>
										<div className='btn-3d bg-gradient-to-r from-primary to-accent p-3 rounded-full text-white'>
											{product.icon}
										</div>
										<div>
											<h3 className='text-2xl font-bold text-text'>
												{product.title}
											</h3>
										</div>
									</div>

									<div className='relative mb-6 overflow-hidden rounded-xl'>
										<img
											src={product.image}
											alt={product.title}
											className='w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110'
											loading='lazy'
										/>
										<div className='absolute inset-0 bg-gradient-to-t from-black/20 to-transparent'></div>
									</div>

									<p className='text-text/70 mb-4'>{product.description}</p>

									<div className='flex flex-wrap gap-2'>
										{product.features.map((feature, featureIndex) => (
											<span
												key={featureIndex}
												className='px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium'>
												{feature}
											</span>
										))}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</section>
	);
};

export default ProductShowcase;
