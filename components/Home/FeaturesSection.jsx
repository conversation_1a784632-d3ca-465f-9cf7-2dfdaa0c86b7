import { Search } from 'lucide-react';
import {
	FaUpload,
	FaLock,
	FaUserShield,
	FaUsers,
	FaTrash,
} from 'react-icons/fa';

const FeaturesSection = () => {
	const features = [
		{
			icon: <FaUpload className='text-2xl text-white' />,
			title: 'Quick Submission',
			description:
				'Upload your documents in just a few clicks and send them directly to a vendor.',
			gradient: 'from-primary to-accent',
		},
		{
			icon: <FaLock className='text-2xl text-white' />,
			title: 'Secure Storage',
			description:
				'Your documents are securely stored and accessible only by authorized admins.',
			gradient: 'from-primary to-secondary',
		},
		{
			icon: <FaUserShield className='text-2xl text-white' />,
			title: 'Admin Managed',
			description:
				'Admins can efficiently review, manage, and delete submissions as needed.',
			gradient: 'from-primary to-accent',
		},
		{
			icon: <FaUsers className='text-2xl text-white' />,
			title: 'Become a Vendor',
			description:
				'Request to become a vendor and allow others to submit projects to you.',
			gradient: 'from-primary to-secondary',
		},
		{
			icon: <Search className='text-2xl text-white' />,
			title: 'Find Local Vendors',
			description:
				'Easily locate and compare vendors in your area. Filter by location, ratings, services, and pricing to find the perfect match for your needs.',
			gradient: 'from-primary to-accent',
		},
		{
			icon: <FaTrash className='text-2xl text-white' />,
			title: 'Easy Tracking',
			description:
				'Track your submissions with details like sender and document name for better organization.',
			gradient: 'from-primary to-secondary',
		},
	];

	return (
		<section
			className='py-24 px-6 relative overflow-hidden'
			aria-labelledby='features-heading'>
			<div
				className='absolute inset-0 bg-gradient-to-b from-transparent via-primary/10 to-transparent'
				aria-hidden='true'></div>

			<div className='relative z-10 max-w-6xl mx-auto'>
				<header className='text-center mb-16'>
					<div className='inline-block px-4 py-1 rounded-full bg-primary/20 text-primary text-sm font-medium mb-4 glow-effect'>
						Features
					</div>
					<h2
						id='features-heading'
						className='text-4xl font-bold mb-6 text-text'>
						Why Choose UploadDoc?
					</h2>
					<p className='text-xl text-text/80 max-w-2xl mx-auto'>
						Our platform offers a complete solution for document management and
						printing services.
					</p>
				</header>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
					{features.map((feature, index) => (
						<div
							key={index}
							className='group relative'>
							<div
								className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} rounded-xl opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-500`}></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div
									className={`btn-3d w-14 h-14 rounded-full bg-gradient-to-r ${feature.gradient} flex items-center justify-center mb-6`}>
									{feature.icon}
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									{feature.title}
								</h3>
								<p className='text-text/80'>{feature.description}</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	);
};

export default FeaturesSection;
