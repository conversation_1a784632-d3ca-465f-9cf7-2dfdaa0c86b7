import { Star, Quote, User } from 'lucide-react';

const TestimonialsSection = () => {
	const testimonials = [
		{
			id: 1,
			name: '<PERSON>',
			role: 'Student',
			company: 'Federal Polythenic Ado-Ekiti (FPA)',
			image: null, // No image
			rating: 5,
			text: 'UploadDoc made it incredibly easy to submit my documents directly to lecturers without any hassle. The process was smooth, secure, and straightforward—I know my document would not be lost in messages. I think people dealing with lot of document should onboard the platform too!',
		},
		{
			id: 2,
			name: '<PERSON><PERSON>',
			role: 'Lecturer',
			company: 'Federal Polythenic Ado-Ekiti (FPA)',
			image: null, // No image
			rating: 5,
			text: 'Using UploadDoc has brought a new level of order to how I collect and review student assignments. The seamless document workflow eliminates the usual back-and-forth, helping me stay organized and efficient throughout the semester.',
		},
	];

	const renderStars = (rating) => {
		return Array.from({ length: 5 }, (_, index) => (
			<Star
				key={index}
				className={`w-5 h-5 ${
					index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
				}`}
			/>
		));
	};

	return (
		<section className='py-24 px-6 bg-gradient-to-b from-primary/5 to-background'>
			<div className='max-w-6xl mx-auto'>
				<header className='text-center mb-16'>
					<div className='inline-block px-4 py-1 rounded-full bg-primary/20 text-primary text-sm font-medium mb-4 glow-effect'>
						Testimonials
					</div>
					<h2 className='text-4xl font-bold mb-6 text-text'>
						What Our Users Say
					</h2>
					<p className='text-xl text-text/80 max-w-2xl mx-auto'>
						Hear from students, professionals, and vendors who have transformed
						their document workflow with UploadDoc
					</p>
				</header>

				<div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
					{testimonials.map((testimonial) => (
						<div
							key={testimonial.id}
							className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-accent rounded-2xl opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-2xl h-full transition-all duration-300'>
								{/* Quote icon */}
								<div className='absolute top-6 right-6 opacity-20'>
									<Quote className='w-12 h-12 text-primary' />
								</div>

								{/* Rating */}
								<div className='flex items-center gap-1 mb-4'>
									{renderStars(testimonial.rating)}
								</div>

								{/* Testimonial text */}
								<blockquote className='text-text/80 text-lg mb-6 leading-relaxed'>
									"{testimonial.text}"
								</blockquote>

								{/* User info */}
								<div className='flex items-center gap-4'>
									<div className='relative'>
										<User className='w-12 h-12 rounded-full object-cover' />
										<div className='absolute inset-0 rounded-full bg-gradient-to-r from-primary to-accent opacity-0 group-hover:opacity-20 transition-opacity duration-300'></div>
									</div>
									<div>
										<div className='font-semibold text-text'>
											{testimonial.name}
										</div>
										<div className='text-text/70 text-sm'>
											{testimonial.role} • {testimonial.company}
										</div>
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Trust indicators */}
				<div className='mt-16 text-center'>
					<div className='inline-flex items-center justify-center gap-8 flex-wrap'>
						<div className='flex items-center gap-2 text-text/70'>
							<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full'>
								<Star className='w-4 h-4 text-white' />
							</div>
							<span className='font-medium'>Happy Users</span>
						</div>
						<div className='flex items-center gap-2 text-text/70'>
							<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full'>
								<Star className='w-4 h-4 text-white' />
							</div>
							<span className='font-medium'>Verified Vendors</span>
						</div>
						<div className='flex items-center gap-2 text-text/70'>
							<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full'>
								<Star className='w-4 h-4 text-white' />
							</div>
							<span className='font-medium'>99.9% Uptime</span>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default TestimonialsSection;
