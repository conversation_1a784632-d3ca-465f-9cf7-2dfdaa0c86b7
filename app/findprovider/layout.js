import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

export const metadata = generateSEOMetadata({
	title: 'Find Printing Providers',
	description: 'Discover trusted printing providers and vendors near you. Compare services, ratings, and pricing to find the perfect printing solution for your documents.',
	keywords: [
		'find printing providers',
		'printing vendors',
		'print shops near me',
		'cyber cafe near me',
		'document printing services',
		'local printing services',
		'printing companies',
		'print service providers',
		'document printing vendors',
		'printing solutions',
		'commercial printing',
		'printing directory'
	],
	path: '/findprovider',
});

export default function FindProviderLayout({ children }) {
	return children;
}
