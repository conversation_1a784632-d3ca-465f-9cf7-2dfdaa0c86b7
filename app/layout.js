import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from 'next/font/google';
import './globals.css';
import { AuthProvider } from './context/AuthContext';
import { DataProvider } from './context/DataContext';
import { ThemeProvider } from './context/ThemeContext';
import { AnalyticsProvider } from './context/AnalyticsContext';
import Footer from '@/components/Footer';
export const metadata = {
	title: {
		default: 'UploadDoc',
		template: '%s | UploadDoc',
	},
	description:
		'UploadDoc is a powerful document management and printing platform for students, professionals, and vendors. Submit, track, manage, and print documents with ease. Find trusted printing providers around you and organize all your documents in one place.',
	keywords: [
		'document management system',
		'document management platform',
		'upload documents online',
		'document printing',
		'find printing services',
		'cyber cafe near me',
		'online printing platform',
		'submit documents for printing',
		'print documents online',
		'student project upload',
		'vendor document portal',
		'UploadDoc',
		'document submission',
		'secure document storage',
		'printing vendors',
		'document tracking',
		'file management',
		'digital document platform',
		'uploaddoc',
	],
	authors: [{ name: 'UploadDoc Team', url: 'https://uploaddoc.app' }],
	creator: 'UploadDoc Team',
	publisher: 'UploadDoc',
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true,
			'max-video-preview': -1,
			'max-image-preview': 'large',
			'max-snippet': -1,
		},
	},
	alternates: {
		canonical: 'https://uploaddoc.app',
	},
	openGraph: {
		title: 'UploadDoc | Document Management & Printing Platform',
		description:
			'Upload, organize, and manage documents online. Easily connect with print vendors around you for fast, secure document printing and project submissions.',
		url: 'https://uploaddoc.app',
		siteName: 'UploadDoc',
		type: 'website',
		locale: 'en_US',
		images: [
			{
				url: 'https://uploaddoc.app/uploaddoc.png',
				width: 1200,
				height: 630,
				alt: 'UploadDoc - Document Management Platform',
			},
		],
	},
	twitter: {
		card: 'summary_large_image',
		title: 'UploadDoc | Document Management & Printing Platform',
		description:
			'UploadDoc helps you submit and print documents quickly and securely. Find nearby vendors and manage all your document needs in one place.',
		images: ['https://uploaddoc.app/uploaddoc.png'],
		creator: '@uploaddoc',
		site: '@uploaddoc',
	},
	verification: {
		google: 'your-google-verification-code', // Replace with actual verification code
		yandex: 'your-yandex-verification-code', // Replace with actual verification code
		yahoo: 'your-yahoo-verification-code', // Replace with actual verification code
	},
	category: 'technology',
	classification: 'Document Management Software',
	other: {
		'mobile-web-app-capable': 'yes',
		'apple-mobile-web-app-capable': 'yes',
		'apple-mobile-web-app-status-bar-style': 'default',
		'format-detection': 'telephone=no',
	},
};

export default function RootLayout({ children }) {
	return (
		<html
			lang='en'
			dir='ltr'>
			<head>
				<meta charSet='utf-8' />
				<meta
					name='viewport'
					content='width=device-width, initial-scale=1, shrink-to-fit=no'
				/>
				<meta
					httpEquiv='X-UA-Compatible'
					content='IE=edge'
				/>

				{/* Primary Meta Tags */}
				<title>{metadata.title.default}</title>
				<meta
					name='title'
					content={metadata.title.default}
				/>
				<meta
					name='description'
					content={metadata.description}
				/>
				<meta
					name='keywords'
					content={metadata.keywords.join(', ')}
				/>
				<meta
					name='author'
					content={metadata.authors[0].name}
				/>
				<meta
					name='creator'
					content={metadata.creator}
				/>
				<meta
					name='publisher'
					content={metadata.publisher}
				/>
				<meta
					name='robots'
					content='index, follow'
				/>
				<meta
					name='googlebot'
					content='index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'
				/>

				{/* Canonical URL */}
				<link
					rel='canonical'
					href={metadata.alternates.canonical}
				/>

				{/* Open Graph / Facebook */}
				<meta
					property='og:type'
					content={metadata.openGraph.type}
				/>
				<meta
					property='og:url'
					content={metadata.openGraph.url}
				/>
				<meta
					property='og:title'
					content={metadata.openGraph.title}
				/>
				<meta
					property='og:description'
					content={metadata.openGraph.description}
				/>
				<meta
					property='og:image'
					content={metadata.openGraph.images[0].url}
				/>
				<meta
					property='og:image:width'
					content={metadata.openGraph.images[0].width.toString()}
				/>
				<meta
					property='og:image:height'
					content={metadata.openGraph.images[0].height.toString()}
				/>
				<meta
					property='og:image:alt'
					content={metadata.openGraph.images[0].alt}
				/>
				<meta
					property='og:site_name'
					content={metadata.openGraph.siteName}
				/>
				<meta
					property='og:locale'
					content={metadata.openGraph.locale}
				/>

				{/* Twitter */}
				<meta
					property='twitter:card'
					content={metadata.twitter.card}
				/>
				<meta
					property='twitter:url'
					content={metadata.openGraph.url}
				/>
				<meta
					property='twitter:title'
					content={metadata.twitter.title}
				/>
				<meta
					property='twitter:description'
					content={metadata.twitter.description}
				/>
				<meta
					property='twitter:image'
					content={metadata.twitter.images[0]}
				/>
				<meta
					property='twitter:creator'
					content={metadata.twitter.creator}
				/>
				<meta
					property='twitter:site'
					content={metadata.twitter.site}
				/>

				{/* Mobile and PWA */}
				<meta
					name='mobile-web-app-capable'
					content='yes'
				/>
				<meta
					name='apple-mobile-web-app-capable'
					content='yes'
				/>
				<meta
					name='apple-mobile-web-app-status-bar-style'
					content='default'
				/>
				<meta
					name='format-detection'
					content='telephone=no'
				/>
				<meta
					name='theme-color'
					content='#3B82F6'
				/>

				{/* Favicon and Icons */}
				<link
					rel='icon'
					href='/favicon.ico'
				/>
				<link
					rel='icon'
					type='image/png'
					sizes='32x32'
					href='/icon.png'
				/>
				<link
					rel='apple-touch-icon'
					href='/icon.png'
				/>
				<link
					rel='manifest'
					href='/manifest.json'
				/>

				{/* Structured Data */}
				<script
					type='application/ld+json'
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							'@context': 'https://schema.org',
							'@type': 'WebApplication',
							name: 'UploadDoc',
							description: metadata.description,
							url: 'https://uploaddoc.app',
							applicationCategory: 'BusinessApplication',
							operatingSystem: 'Web Browser',
							offers: {
								'@type': 'Offer',
								price: '0',
								priceCurrency: 'USD',
							},
							creator: {
								'@type': 'Organization',
								name: 'UploadDoc Team',
								url: 'https://uploaddoc.app',
							},
							featureList: [
								'Document Upload',
								'Secure Storage',
								'Vendor Management',
								'Document Tracking',
								'Print Services',
							],
							screenshot: 'https://uploaddoc.app/uploaddoc.png',
						}),
					}}
				/>

				{/* Organization Structured Data */}
				<script
					type='application/ld+json'
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							'@context': 'https://schema.org',
							'@type': 'Organization',
							name: 'UploadDoc',
							url: 'https://uploaddoc.app',
							logo: 'https://uploaddoc.app/uploaddoc.png',
							description: metadata.description,
							sameAs: ['https://twitter.com/uploaddoc'],
							contactPoint: {
								'@type': 'ContactPoint',
								contactType: 'customer service',
								url: 'https://uploaddoc.app',
							},
						}),
					}}
				/>
			</head>
			<body>
				<AuthProvider>
					<DataProvider>
						<ThemeProvider>
							<AnalyticsProvider>
								{children}
								<Footer aria-hidden='true' />
							</AnalyticsProvider>
						</ThemeProvider>
					</DataProvider>
				</AuthProvider>
			</body>
		</html>
	);
}
