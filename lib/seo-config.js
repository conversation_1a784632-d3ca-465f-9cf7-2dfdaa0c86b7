// SEO Configuration and Constants

export const SEO_CONFIG = {
	// Site Information
	siteName: 'UploadDoc',
	siteUrl: 'https://uploaddoc.app',
	defaultTitle: 'UploadDoc | Document Management & Printing Platform',
	titleTemplate: '%s | UploadDoc',

	// Default Meta Information
	defaultDescription:
		'UploadDoc is a powerful document management and printing platform for students, professionals, and vendors. Submit, track, manage, and print documents with ease.',
	defaultImage: 'https://uploaddoc.app/uploaddoc.png',
	defaultImageAlt: 'UploadDoc - Document Management Platform',

	// Social Media
	twitterHandle: '@uploaddoc',
	facebookAppId: '', // Add if you have one

	// Business Information
	author: 'UploadDoc Team',
	publisher: 'UploadDoc',
	category: 'technology',
	classification: 'Document Management Software',

	// Technical SEO
	themeColor: '#3B82F6',
	backgroundColor: '#ffffff',
	locale: 'en_US',
	language: 'en',

	// Verification Codes (replace with actual codes)
	verification: {
		google: 'your-google-verification-code',
		bing: 'your-bing-verification-code',
		yandex: 'your-yandex-verification-code',
		yahoo: 'your-yahoo-verification-code',
	},
};

// Common Keywords for all pages
export const GLOBAL_KEYWORDS = [
	'document management system',
	'document management platform',
	'upload documents online',
	'document printing',
	'find printing services',
	'cyber cafe near me',
	'online printing platform',
	'submit documents for printing',
	'print documents online',
	'student project upload',
	'vendor document portal',
	'UploadDoc',
	'document submission',
	'secure document storage',
	'printing vendors',
	'document tracking',
	'file management',
	'digital document platform',
];

// Page-specific SEO configurations
export const PAGE_SEO_CONFIG = {
	home: {
		title: 'Document Management & Printing Platform',
		description:
			'Upload, organize, and manage documents online. Connect with trusted printing vendors for secure document printing and project submissions.',
		keywords: [
			'document management platform',
			'online document storage',
			'printing platform',
			'document upload',
			'secure file sharing',
		],
	},
	submit: {
		title: 'Submit Documents',
		description:
			'Upload and submit your documents securely to printing vendors. Track your submissions and manage your document printing needs.',
		keywords: [
			'submit documents',
			'upload documents',
			'document submission',
			'print documents',
			'secure document upload',
		],
	},
	findprovider: {
		title: 'Find Printing Providers',
		description:
			'Discover trusted printing providers and vendors near you. Compare services, ratings, and pricing.',
		keywords: [
			'find printing providers',
			'printing vendors',
			'print shops near me',
			'cyber cafe near me',
			'local printing services',
		],
	},
	about: {
		title: 'About UploadDoc',
		description:
			'Learn about UploadDoc, the leading document management and printing platform.',
		keywords: [
			'about uploaddoc',
			'company information',
			'our mission',
			'platform features',
		],
	},
	upgradetoadmin: {
		title: 'Become a Vendor',
		description:
			'Join UploadDoc as a printing vendor and expand your business. Connect with customers looking for document printing services.',
		keywords: [
			'become a vendor',
			'printing vendor',
			'vendor registration',
			'printing business',
			'business opportunity',
		],
	},
	download: {
		title: 'Download UploadDoc App',
		description:
			'Download the UploadDoc mobile app for iOS and Android. Manage your documents on the go.',
		keywords: [
			'download uploaddoc app',
			'mobile app',
			'ios app',
			'android app',
			'document management app',
		],
	},
};
export const DEFAULT_SEO = {
	siteName: 'UploadDoc',
	siteUrl: 'https://uploaddoc.app',
	defaultTitle: 'UploadDoc | Document Management & Printing Platform',
	defaultDescription:
		'UploadDoc is a powerful document management and printing platform for students, professionals, and vendors. Submit, track, manage, and print documents with ease.',
	defaultImage: 'https://uploaddoc.app/uploaddoc.png',
	twitterHandle: '@uploaddoc',
	author: 'UploadDoc Team',
};

export const COMMON_KEYWORDS = [
	'document management system',
	'document management platform',
	'upload documents online',
	'document printing',
	'find printing services',
	'cyber cafe near me',
	'online printing platform',
	'submit documents for printing',
	'print documents online',
	'student project upload',
	'vendor document portal',
	'UploadDoc',
	'document submission',
	'secure document storage',
	'printing vendors',
	'document tracking',
	'file management',
	'digital document platform',
];

// Structured Data Templates
export const STRUCTURED_DATA_TEMPLATES = {
	organization: {
		'@context': 'https://schema.org',
		'@type': 'Organization',
		name: SEO_CONFIG.siteName,
		url: SEO_CONFIG.siteUrl,
		logo: SEO_CONFIG.defaultImage,
		description: SEO_CONFIG.defaultDescription,
		sameAs: [
			`https://twitter.com/${SEO_CONFIG.twitterHandle.replace('@', '')}`,
		],
		contactPoint: {
			'@type': 'ContactPoint',
			contactType: 'customer service',
			url: SEO_CONFIG.siteUrl,
		},
	},

	website: {
		'@context': 'https://schema.org',
		'@type': 'WebSite',
		name: SEO_CONFIG.siteName,
		url: SEO_CONFIG.siteUrl,
		description: SEO_CONFIG.defaultDescription,
		potentialAction: {
			'@type': 'SearchAction',
			target: `${SEO_CONFIG.siteUrl}/findprovider?search={search_term_string}`,
			'query-input': 'required name=search_term_string',
		},
	},

	webApplication: {
		'@context': 'https://schema.org',
		'@type': 'WebApplication',
		name: SEO_CONFIG.siteName,
		description: SEO_CONFIG.defaultDescription,
		url: SEO_CONFIG.siteUrl,
		applicationCategory: 'BusinessApplication',
		operatingSystem: 'Web Browser',
		offers: {
			'@type': 'Offer',
			price: '0',
			priceCurrency: 'USD',
		},
		creator: {
			'@type': 'Organization',
			name: SEO_CONFIG.author,
			url: SEO_CONFIG.siteUrl,
		},
		featureList: [
			'Document Upload',
			'Secure Storage',
			'Vendor Management',
			'Document Tracking',
			'Print Services',
		],
		screenshot: SEO_CONFIG.defaultImage,
	},
};

// SEO Best Practices Checklist
export const SEO_CHECKLIST = {
	technical: [
		'✅ Sitemap.xml generated',
		'✅ Robots.txt configured',
		'✅ Meta tags optimized',
		'✅ Open Graph tags added',
		'✅ Twitter Cards configured',
		'✅ Structured data implemented',
		'✅ Canonical URLs set',
		'✅ Mobile-friendly viewport',
		'✅ Performance headers added',
		'✅ PWA manifest created',
	],
	content: [
		'✅ Semantic HTML structure',
		'✅ Proper heading hierarchy',
		'✅ Alt text for images',
		'✅ Descriptive link text',
		'✅ Breadcrumb navigation',
		'✅ FAQ structured data',
		'✅ Page-specific metadata',
		'✅ Keyword optimization',
	],
	performance: [
		'✅ Image optimization',
		'✅ Compression enabled',
		'✅ Caching headers',
		'✅ Security headers',
		'✅ Core Web Vitals optimization',
	],
	monitoring: [
		'🔄 Google Search Console setup needed',
		'🔄 Google Analytics setup needed',
		'🔄 Bing Webmaster Tools setup needed',
		'🔄 Schema markup validation needed',
		'🔄 Page speed testing needed',
	],
};
