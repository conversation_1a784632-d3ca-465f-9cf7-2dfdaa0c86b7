// SEO utility functions and constants

import { COMMON_KEYWORDS, DEFAULT_SEO } from './seo-config';

/**
 * Generate metadata for a page
 * @param {Object} options - SEO options
 * @param {string} options.title - Page title
 * @param {string} options.description - Page description
 * @param {string[]} options.keywords - Additional keywords
 * @param {string} options.path - Page path
 * @param {string} options.image - Page image
 * @param {string} options.type - Open Graph type
 * @returns {Object} Next.js metadata object
 */
export function generateMetadata({
	title,
	description = DEFAULT_SEO.defaultDescription,
	keywords = [],
	path = '',
	image = DEFAULT_SEO.defaultImage,
	type = 'website',
}) {
	const fullTitle = title
		? `${title} | ${DEFAULT_SEO.siteName}`
		: DEFAULT_SEO.defaultTitle;
	const url = `${DEFAULT_SEO.siteUrl}${path}`;
	const allKeywords = [...COMMON_KEYWORDS, ...keywords];

	return {
		title: fullTitle,
		description,
		keywords: allKeywords,
		authors: [{ name: DEFAULT_SEO.author, url: DEFAULT_SEO.siteUrl }],
		creator: DEFAULT_SEO.author,
		publisher: DEFAULT_SEO.siteName,
		robots: {
			index: true,
			follow: true,
			googleBot: {
				index: true,
				follow: true,
				'max-video-preview': -1,
				'max-image-preview': 'large',
				'max-snippet': -1,
			},
		},
		alternates: {
			canonical: url,
		},
		openGraph: {
			title: fullTitle,
			description,
			url,
			siteName: DEFAULT_SEO.siteName,
			type,
			locale: 'en_US',
			images: [
				{
					url: image,
					width: 1200,
					height: 630,
					alt: `${DEFAULT_SEO.siteName} - ${
						title || 'Document Management Platform'
					}`,
				},
			],
		},
		twitter: {
			card: 'summary_large_image',
			title: fullTitle,
			description,
			images: [image],
			creator: DEFAULT_SEO.twitterHandle,
			site: DEFAULT_SEO.twitterHandle,
		},
	};
}

/**
 * Generate structured data for a page
 * @param {Object} options - Structured data options
 * @param {string} options.type - Schema.org type
 * @param {string} options.name - Name/title
 * @param {string} options.description - Description
 * @param {string} options.url - Page URL
 * @param {Object} options.additionalData - Additional schema data
 * @returns {Object} JSON-LD structured data
 */
export function generateStructuredData({
	type = 'WebPage',
	name,
	description,
	url,
	additionalData = {},
}) {
	const baseData = {
		'@context': 'https://schema.org',
		'@type': type,
		name,
		description,
		url,
		isPartOf: {
			'@type': 'WebSite',
			name: DEFAULT_SEO.siteName,
			url: DEFAULT_SEO.siteUrl,
		},
		...additionalData,
	};

	return baseData;
}

/**
 * Generate breadcrumb structured data
 * @param {Array} breadcrumbs - Array of breadcrumb items
 * @returns {Object} JSON-LD breadcrumb structured data
 */
export function generateBreadcrumbStructuredData(breadcrumbs) {
	return {
		'@context': 'https://schema.org',
		'@type': 'BreadcrumbList',
		itemListElement: breadcrumbs.map((item, index) => ({
			'@type': 'ListItem',
			position: index + 1,
			name: item.name,
			item: `${DEFAULT_SEO.siteUrl}${item.path}`,
		})),
	};
}

/**
 * Generate FAQ structured data
 * @param {Array} faqs - Array of FAQ items with question and answer
 * @returns {Object} JSON-LD FAQ structured data
 */
export function generateFAQStructuredData(faqs) {
	return {
		'@context': 'https://schema.org',
		'@type': 'FAQPage',
		mainEntity: faqs.map((faq) => ({
			'@type': 'Question',
			name: faq.question,
			acceptedAnswer: {
				'@type': 'Answer',
				text: faq.answer,
			},
		})),
	};
}

/**
 * Generate service structured data
 * @param {Object} service - Service information
 * @returns {Object} JSON-LD service structured data
 */
export function generateServiceStructuredData(service) {
	return {
		'@context': 'https://schema.org',
		'@type': 'Service',
		name: service.name,
		description: service.description,
		provider: {
			'@type': 'Organization',
			name: DEFAULT_SEO.siteName,
			url: DEFAULT_SEO.siteUrl,
		},
		areaServed: service.areaServed || 'Worldwide',
		serviceType: service.serviceType,
		...service.additionalData,
	};
}
